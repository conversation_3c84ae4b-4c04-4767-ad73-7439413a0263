#!/usr/bin/env python3
"""
Test script to verify that datacenters continue trying after token refresh failures.
"""

import sys
import os


def check_stop_processing_logic():
    """Check that stop_processing is only used for rate limiting, not token refresh failures"""
    print("=== Checking Datacenter Continuation Logic ===")

    # Check zoho_api.py for correct stop_processing usage
    with open("src/zoho_api.py", "r") as f:
        content = f.read()

    print("\n--- Analyzing stop_processing usage ---")

    # Find all occurrences of stop_processing
    lines = content.split("\n")
    stop_processing_lines = []

    for i, line in enumerate(lines, 1):
        if "stop_processing" in line and "True" in line:
            stop_processing_lines.append((i, line.strip()))

    print(f"Found {len(stop_processing_lines)} stop_processing: True occurrences:")

    for line_num, line in stop_processing_lines:
        print(f"  Line {line_num}: {line}")

    # Check specific conditions
    rate_limit_stops = 0
    token_refresh_stops = 0

    for line_num, line in stop_processing_lines:
        # Look at context around the line
        start_idx = max(0, line_num - 10)
        end_idx = min(len(lines), line_num + 5)
        context = "\n".join(lines[start_idx:end_idx])

        if any(
            term in context.lower()
            for term in ["rate limit", "too many requests", "rate_limited"]
        ):
            rate_limit_stops += 1
            print(f"  ✓ Line {line_num}: Correctly stops for rate limiting")
        elif any(
            term in context.lower()
            for term in ["access forbidden", "insufficient permissions", "403"]
        ):
            rate_limit_stops += 1  # Count as correct - 403 should stop all DCs
            print(f"  ✓ Line {line_num}: Correctly stops for access forbidden (403)")
        elif any(
            term in context.lower()
            for term in ["token_refresh", "refresh_failed", "auth_failure"]
        ):
            token_refresh_stops += 1
            print(f"  ✗ Line {line_num}: Incorrectly stops for token refresh")
        else:
            print(f"  ? Line {line_num}: Unknown context")

    print(f"\n--- Summary ---")
    print(f"Rate limiting stops (correct): {rate_limit_stops}")
    print(f"Token refresh stops (incorrect): {token_refresh_stops}")

    if token_refresh_stops == 0:
        print("✓ PASS: No incorrect stop_processing for token refresh failures")
        print("✓ Datacenters will continue trying after token refresh failures")
    else:
        print("✗ FAIL: Found incorrect stop_processing for token refresh failures")
        print("✗ This would prevent trying other datacenters")

    return token_refresh_stops == 0


def check_rate_limiting_logic():
    """Check that rate limiting properly stops all datacenter attempts"""
    print("\n=== Checking Rate Limiting Logic ===")

    with open("src/zoho_api.py", "r") as f:
        content = f.read()

    # Check for rate limiting detection
    checks = [
        ("too many requests", "Rate limit detection in 400 responses"),
        ("RATE_LIMITED", "Rate limited error type"),
        ("RATE_LIMIT_EXCEEDED", "Rate limit exceeded error type"),
        ("self._rate_limited", "Rate limited state tracking"),
    ]

    print("\n--- Rate Limiting Features ---")
    for check, description in checks:
        if check in content:
            print(f"✓ {description}: FOUND")
        else:
            print(f"✗ {description}: MISSING")

    # Check that 429 responses have stop_processing
    if (
        '"stop_processing": True' in content
        and "response.status_code == 429" in content
    ):
        print("✓ 429 responses correctly stop processing")
    else:
        print("✗ 429 responses may not stop processing")


def main():
    """Main test function"""
    print("=== Testing Datacenter Continuation After Token Refresh Failures ===")

    continuation_ok = check_stop_processing_logic()
    check_rate_limiting_logic()

    print("\n=== Expected Behavior ===")
    print("1. Token refresh failure in DC1 → Continue trying DC2, DC3, etc.")
    print("2. Rate limiting in any DC → Stop trying all other DCs")
    print("3. Authentication error → Continue trying other DCs with original token")
    print("4. Only rate limiting should stop the datacenter loop")

    if continuation_ok:
        print(
            "\n✓ OPTIMIZATION VERIFIED: Datacenters will continue after token refresh failures"
        )
        print("✓ This should resolve the 'All 6 datacenters failed' issue")
    else:
        print(
            "\n✗ ISSUE FOUND: Some token refresh failures still stop datacenter attempts"
        )
        print("✗ This may still cause premature 'All datacenters failed' errors")


if __name__ == "__main__":
    main()
