#!/usr/bin/env python3
"""
Test script to verify configuration loading
"""

import os
import sys

# Add src to path to import modules directly
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_env_loading():
    """Test that environment variables are loaded correctly"""
    print("=== Testing Environment Variable Loading ===")
    
    # Load .env file manually
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check environment variables
    client_id = os.getenv("ZOHO_CLIENT_ID")
    client_secret = os.getenv("ZOHO_CLIENT_SECRET")
    environment = os.getenv("ZOHO_ENVIRONMENT")
    
    print(f"ZOHO_CLIENT_ID: {'present' if client_id else 'missing'}")
    print(f"ZOHO_CLIENT_SECRET: {'present' if client_secret else 'missing'}")
    print(f"ZOHO_ENVIRONMENT: {environment}")
    
    if client_id:
        print(f"Client ID starts with: {client_id[:10]}...")
    if client_secret:
        print(f"Client Secret starts with: {client_secret[:10]}...")
    
    return bool(client_id and client_secret)

def test_config_loading():
    """Test that the configuration classes load correctly"""
    print("\n=== Testing Configuration Class Loading ===")
    
    try:
        from config import get_settings
        settings = get_settings()
        
        print(f"Zoho client_id: {'present' if settings.zoho.client_id else 'missing'}")
        print(f"Zoho client_secret: {'present' if settings.zoho.client_secret else 'missing'}")
        print(f"Zoho environment: {settings.zoho.environment}")
        print(f"Zoho API base URL: {settings.zoho.api_base_url}")
        
        if settings.zoho.client_id:
            print(f"Config Client ID starts with: {settings.zoho.client_id[:10]}...")
        if settings.zoho.client_secret:
            print(f"Config Client Secret starts with: {settings.zoho.client_secret[:10]}...")
        
        return bool(settings.zoho.client_id and settings.zoho.client_secret)
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return False

def test_zoho_provider():
    """Test that ZohoProvider can be created with the configuration"""
    print("\n=== Testing ZohoProvider Creation ===")
    
    try:
        from config import get_settings
        from zoho_oauth import get_zoho_provider
        
        settings = get_settings()
        provider = get_zoho_provider(settings.zoho)
        
        print(f"ZohoProvider created successfully")
        print(f"Provider client_id: {'present' if provider.config.client_id else 'missing'}")
        print(f"Provider client_secret: {'present' if provider.config.client_secret else 'missing'}")
        print(f"OAuth token URL: {provider.oauth_token_url}")
        
        return bool(provider.config.client_id and provider.config.client_secret)
        
    except Exception as e:
        print(f"Error creating ZohoProvider: {e}")
        return False

def main():
    """Main test function"""
    print("=== Zoho Configuration Test ===")
    
    tests = [
        ("Environment Variables", test_env_loading),
        ("Configuration Loading", test_config_loading),
        ("ZohoProvider Creation", test_zoho_provider),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n--- {test_name} ---")
            if test_func():
                print(f"✓ {test_name} PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All configuration tests passed!")
    else:
        print("❌ Some configuration tests failed.")
        print("\nTroubleshooting:")
        print("1. Make sure .env file exists in the project root")
        print("2. Check that ZOHO_CLIENT_ID and ZOHO_CLIENT_SECRET are set")
        print("3. Verify the values don't have extra spaces or quotes")

if __name__ == "__main__":
    main()
