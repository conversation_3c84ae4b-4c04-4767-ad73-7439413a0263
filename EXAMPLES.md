# Zoho CRM MCP Server - Complete Examples Guide

This guide provides real-world examples for all OAuth setup commands and MCP tool calls.

## OAuth Setup Examples

### 1. OAuth Helper Commands

#### Generate Authorization URL

```bash
python oauth_helper.py --get-auth-url
```

**Example Output:**

```
🔗 Generating authorization URL...
📍 Environment: US
🌐 Accounts Domain: https://accounts.zoho.com
🔑 Client ID: 1000.4E1SXJM8YW6HAPRY1QMM9NB5T3117B

📋 STEP 1: Visit this URL in your browser:
   https://accounts.zoho.com/oauth/v2/auth?response_type=code&client_id=1000.4E1SXJM8YW6HAPRY1QMM9NB5T3117B&scope=ZohoCRM.modules.ALL%2CZohoCRM.settings.ALL%2CZohoCRM.users.ALL&redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fcallback&access_type=offline

📋 STEP 2: Grant permissions and copy the 'code' parameter from the redirect URL
📋 STEP 3: Run this command with your code:
   python oauth_helper.py --exchange-code YOUR_CODE_HERE --redirect-uri http://localhost:8000/callback
```

#### Exchange Authorization Code

```bash
python oauth_helper.py --exchange-code 1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0
```

**Example Output:**

```
🔄 Exchanging authorization code for tokens...
✅ Success! Received tokens:
   Access Token: eyJhbGciOiJSUzI1NiIsInR5...
   Refresh Token: 1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0
   Expires In: 3600 seconds

🧪 Testing refresh token...
✅ Token test successful! SDK initialized correctly.

📝 UPDATE YOUR .env FILE:
   ZOHO_REFRESH_TOKEN=1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0

🎉 You can now start your MCP server!
```

#### Test Refresh Token

```bash
python oauth_helper.py --test-token 1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0
```

**Example Output:**

```
🧪 Testing refresh token...
✅ Token test successful! SDK initialized correctly.
🎉 Token is valid! You can use it in your .env file.
```

### 2. OAuth Callback Server

#### Start Callback Server with Auto-Open

```bash
python oauth_callback_server.py --auto-open
```

**Example Output:**

```
🌐 OAuth Callback Server running on http://localhost:8000
📍 Callback URL: http://localhost:8000/callback
⏳ Waiting for OAuth authorization...
💡 In another terminal, run: python oauth_helper.py --get-auth-url
🛑 Press Ctrl+C to stop the server
🔗 Opening authorization URL in browser...

✅ Authorization Code Received!
📋 Code: 1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0

🚀 Next step: Run this command:
   python oauth_helper.py --exchange-code 1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0
```

## MCP Tools Examples

### 1. create_record Tool

#### Create a Lead

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": "{\"Last_Name\": \"Smith\", \"First_Name\": \"John\", \"Company\": \"Acme Corp\", \"Email\": \"<EMAIL>\", \"Phone\": \"******-123-4567\", \"Lead_Source\": \"Web Research\", \"Industry\": \"Technology\", \"Annual_Revenue\": 1000000, \"City\": \"San Francisco\", \"State\": \"California\", \"Country\": \"USA\"}"
  }
}
```

**Important Note:**

The `record_data` parameter now accepts a **JSON stringified object** instead of a direct object. This means you need to pass the record data as a JSON string that will be parsed by the server.

**Data Type Handling:**

- **Numeric fields** (Annual_Revenue, Amount, etc.): Can be provided as integers or floats - they will be automatically converted to the correct type
- **Picklist fields** (Lead_Source, Industry, etc.): Provide as strings - they will be automatically wrapped in Choice objects
- **Text fields**: Provide as strings
- **Date fields**: Provide in YYYY-MM-DD format
- **DateTime fields**: Provide in ISO format

**Example Response:**

```json
{
  "success": true,
  "id": "4876876000000240001",
  "message": "record added",
  "status": "success",
  "details": {
    "id": "4876876000000240001",
    "Created_Time": "2025-02-07T09:32:15+00:00",
    "Modified_Time": "2025-02-07T09:32:15+00:00"
  }
}
```

**Note:** Picklist fields like `Lead_Source` and `Industry` are automatically handled by the server. The system recognizes common picklist fields and wraps their values in the required `Choice` objects internally.

#### Create a Contact

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Contacts",
    "record_data": "{\"Last_Name\": \"Johnson\", \"First_Name\": \"Sarah\", \"Email\": \"<EMAIL>\", \"Phone\": \"******-987-6543\", \"Account_Name\": \"Tech Solutions Inc\", \"Title\": \"Marketing Director\", \"Department\": \"Marketing\", \"Mailing_Street\": \"123 Business Ave\", \"Mailing_City\": \"New York\", \"Mailing_State\": \"NY\", \"Mailing_Zip\": \"10001\", \"Mailing_Country\": \"USA\"}"
  }
}
```

#### Create an Account

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Accounts",
    "record_data": "{\"Account_Name\": \"Global Enterprises Ltd\", \"Website\": \"https://globalenterprises.com\", \"Phone\": \"******-111-2222\", \"Industry\": \"Manufacturing\", \"Annual_Revenue\": ********, \"Employees\": 500, \"Billing_Street\": \"456 Corporate Blvd\", \"Billing_City\": \"Chicago\", \"Billing_State\": \"IL\", \"Billing_Code\": \"60601\", \"Billing_Country\": \"USA\", \"Account_Type\": \"Customer\"}"
  }
}
```

### 2. fetch_records Tool

#### Fetch Leads with Filtering

```json
{
  "name": "fetch_records",
  "arguments": {
    "module": "Leads",
    "fields": [
      "Last_Name",
      "First_Name",
      "Email",
      "Company",
      "Lead_Source",
      "Created_Time"
    ],
    "criteria": {
      "Lead_Source": "Web Research",
      "Industry": "Technology"
    },
    "sort_by": "Created_Time",
    "sort_order": "desc",
    "page": 1,
    "per_page": 10,
    "max_records": 50
  }
}
```

**Example Response:**

```json
{
  "success": true,
  "records": [
    {
      "id": "4876876000000240001",
      "Last_Name": "Smith",
      "First_Name": "John",
      "Email": "<EMAIL>",
      "Company": "Acme Corp",
      "Lead_Source": "Web Research",
      "Created_Time": "2025-02-07T09:32:15+00:00"
    },
    {
      "id": "4876876000000240002",
      "Last_Name": "Davis",
      "First_Name": "Emily",
      "Email": "<EMAIL>",
      "Company": "TechStart Inc",
      "Lead_Source": "Web Research",
      "Created_Time": "2025-02-07T08:15:30+00:00"
    }
  ],
  "total_count": 2,
  "has_more": false
}
```

#### Fetch Contacts with Pagination

```json
{
  "name": "fetch_records",
  "arguments": {
    "module": "Contacts",
    "fields": ["Full_Name", "Email", "Phone", "Account_Name", "Title"],
    "sort_by": "Modified_Time",
    "sort_order": "desc",
    "page": 1,
    "per_page": 25
  }
}
```

#### Fetch Accounts by Revenue

```json
{
  "name": "fetch_records",
  "arguments": {
    "module": "Accounts",
    "fields": [
      "Account_Name",
      "Website",
      "Annual_Revenue",
      "Industry",
      "Phone"
    ],
    "criteria": {
      "Annual_Revenue": ">********"
    },
    "sort_by": "Annual_Revenue",
    "sort_order": "desc",
    "per_page": 20
  }
}
```

### 3. search_records Tool

#### Search Across Multiple Modules

```json
{
  "name": "search_records",
  "arguments": {
    "modules": ["Leads", "Contacts", "Accounts"],
    "search_text": "john smith",
    "fields": ["Full_Name", "Email", "Phone", "Company"],
    "max_results": 50
  }
}
```

**Example Response:**

```json
{
  "success": true,
  "results": {
    "Leads": [
      {
        "id": "4876876000000240001",
        "Full_Name": "John Smith",
        "Email": "<EMAIL>",
        "Phone": "******-123-4567",
        "Company": "Acme Corp"
      }
    ],
    "Contacts": [
      {
        "id": "4876876000000241001",
        "Full_Name": "John Smith",
        "Email": "<EMAIL>",
        "Phone": "******-999-8888",
        "Account_Name": "Enterprise Solutions"
      }
    ],
    "Accounts": []
  }
}
```

#### Search for Email Addresses

```json
{
  "name": "search_records",
  "arguments": {
    "modules": ["Leads", "Contacts"],
    "search_text": "@acme.com",
    "fields": ["Full_Name", "Email", "Phone", "Title"],
    "max_results": 100
  }
}
```

#### Search by Company Name

```json
{
  "name": "search_records",
  "arguments": {
    "modules": ["Leads", "Contacts", "Accounts"],
    "search_text": "Technology Solutions",
    "fields": ["Account_Name", "Company", "Full_Name", "Email"],
    "max_results": 25
  }
}
```

## MCP Resources Examples

### 1. Module Schema Resource

#### Get Leads Module Schema

```
URI: zoho://modules/Leads/schema
```

**Example Response:**

```json
{
  "api_name": "Leads",
  "module_name": "Leads",
  "plural_label": "Leads",
  "singular_label": "Lead",
  "creatable": true,
  "updatable": true,
  "deletable": true,
  "viewable": true,
  "searchable": true,
  "convertible": true,
  "editable": true,
  "emailTemplate": true,
  "profiles": [
    {
      "name": "Administrator",
      "id": "4876876000000026011"
    }
  ]
}
```

#### Get Accounts Module Schema

```
URI: zoho://modules/Accounts/schema
```

#### Get Contacts Module Schema

```
URI: zoho://modules/Contacts/schema
```

### 2. Module Fields Resource

#### Get Leads Fields

```
URI: zoho://modules/Leads/fields
```

**Example Response:**

```json
[
  {
    "api_name": "Last_Name",
    "field_label": "Last Name",
    "data_type": "text",
    "mandatory": true,
    "read_only": false,
    "custom_field": false,
    "max_length": 80
  },
  {
    "api_name": "First_Name",
    "field_label": "First Name",
    "data_type": "text",
    "mandatory": false,
    "read_only": false,
    "custom_field": false,
    "max_length": 40
  },
  {
    "api_name": "Email",
    "field_label": "Email",
    "data_type": "email",
    "mandatory": false,
    "read_only": false,
    "custom_field": false,
    "max_length": 100
  },
  {
    "api_name": "Lead_Source",
    "field_label": "Lead Source",
    "data_type": "picklist",
    "mandatory": false,
    "read_only": false,
    "custom_field": false,
    "pick_list_values": [
      {
        "display_value": "Advertisement",
        "actual_value": "Advertisement"
      },
      {
        "display_value": "Cold Call",
        "actual_value": "Cold Call"
      },
      {
        "display_value": "Website",
        "actual_value": "Website"
      }
    ]
  }
]
```

#### Get Contacts Fields

```
URI: zoho://modules/Contacts/fields
```

#### Get Custom Module Fields

```
URI: zoho://modules/Custom_Module/fields
```

### 3. Picklist Values Resource

#### Get Lead Source Picklist

```
URI: zoho://modules/Leads/picklists/Lead_Source
```

**Example Response:**

```json
[
  {
    "display_value": "Advertisement",
    "actual_value": "Advertisement"
  },
  {
    "display_value": "Cold Call",
    "actual_value": "Cold Call"
  },
  {
    "display_value": "Employee Referral",
    "actual_value": "Employee Referral"
  },
  {
    "display_value": "External Referral",
    "actual_value": "External Referral"
  },
  {
    "display_value": "Online Store",
    "actual_value": "Online Store"
  },
  {
    "display_value": "Partner",
    "actual_value": "Partner"
  },
  {
    "display_value": "Public Relations",
    "actual_value": "Public Relations"
  },
  {
    "display_value": "Sales Email Alias",
    "actual_value": "Sales Email Alias"
  },
  {
    "display_value": "Seminar Partner",
    "actual_value": "Seminar Partner"
  },
  {
    "display_value": "Trade Show",
    "actual_value": "Trade Show"
  },
  {
    "display_value": "Web Download",
    "actual_value": "Web Download"
  },
  {
    "display_value": "Website",
    "actual_value": "Website"
  }
]
```

#### Get Industry Picklist

```
URI: zoho://modules/Leads/picklists/Industry
```

#### Get Account Type Picklist

```
URI: zoho://modules/Accounts/picklists/Account_Type
```

## Environment Configuration Examples

### .env File Example

```env
# Zoho CRM Configuration
ZOHO_CLIENT_ID=1000.4E1SXJM8YW6HAPRY1QMM9NB5T3117B
ZOHO_CLIENT_SECRET=3bb8f05d8ef5692d98dba91622d9e15c286902c79a
ZOHO_REFRESH_TOKEN=1000.db7402ba02041b04c08f07d622d51011.a1b2c3d4e5f6g7h8i9j0
ZOHO_ENVIRONMENT=US

# Server Configuration
SERVER_NAME=Zoho CRM MCP Server
LOG_LEVEL=INFO
DEBUG=false

# HTTP Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
SERVER_PATH=/mcp

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
```

### Different Environment Examples

#### EU Environment

```env
ZOHO_ENVIRONMENT=EU
ZOHO_CLIENT_ID=1000.EU_CLIENT_ID_HERE
ZOHO_CLIENT_SECRET=eu_client_secret_here
ZOHO_REFRESH_TOKEN=1000.eu_refresh_token_here
```

#### India Environment

```env
ZOHO_ENVIRONMENT=IN
ZOHO_CLIENT_ID=1000.IN_CLIENT_ID_HERE
ZOHO_CLIENT_SECRET=in_client_secret_here
ZOHO_REFRESH_TOKEN=1000.in_refresh_token_here
```

## Picklist Field Handling

The Zoho CRM MCP Server automatically handles picklist fields by wrapping string values in the required `Choice` objects. The following fields are automatically detected as picklist fields:

### Standard Picklist Fields by Module

#### Leads

- `Lead_Source`, `Industry`, `Lead_Status`, `Rating`, `Salutation`

#### Contacts

- `Lead_Source`, `Salutation`, `Department`

#### Accounts

- `Industry`, `Account_Type`, `Rating`, `Ownership`

#### Deals

- `Stage`, `Deal_Category_Status`, `Type`, `Lead_Source`

#### Tasks

- `Status`, `Priority`

#### Cases

- `Status`, `Priority`, `Case_Origin`, `Case_Reason`

### Example: Using Picklist Fields

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": "{\"Last_Name\": \"Johnson\", \"Lead_Source\": \"Web Research\", \"Industry\": \"Technology\", \"Rating\": \"Hot\", \"Salutation\": \"Mr.\"}"
  }
}
```

### Valid Lead_Source Values

Based on Zoho CRM API, the accepted values for `Lead_Source` are:

- `"-None-"`, `"Advertisement"`, `"Cold Call"`, `"Employee Referral"`, `"External Referral"`, `"Online Store"`, `"X (Twitter)"`, `"Facebook"`, `"Partner"`, `"Public Relations"`, `"Sales Email Alias"`, `"Seminar Partner"`, `"Internal Seminar"`, `"Trade Show"`, `"Web Download"`, `"Web Research"`, `"Chat"`

**Important:** The server has `pick_list_validation` disabled, which means it will accept any string values for picklist fields. However, for best results, use the valid values listed above for `Lead_Source`.
The server automatically converts these string values to the required `Choice` objects internally.

## Error Handling Examples

### OAuth Errors

#### Invalid Client Error

```bash
python oauth_helper.py --exchange-code invalid_code
```

**Output:**

```
❌ Error: Failed to exchange code for tokens: 400 - {"error":"invalid_client","error_description":"Client authentication failed"}
```

#### Expired Code Error

```bash
python oauth_helper.py --exchange-code 1000.expired_code_here
```

**Output:**

```
❌ Error: Failed to exchange code for tokens: 400 - {"error":"invalid_grant","error_description":"authorization code is invalid or expired"}
```

### MCP Tool Errors

#### Invalid Module Error

```json
{
  "name": "create_record",
  "arguments": {
    "module": "InvalidModule",
    "record_data": "{\"Name\": \"Test\"}"
  }
}
```

**Response:**

```json
{
  "success": false,
  "error": "INVALID_MODULE",
  "error_type": "APIException",
  "message": "The given module is not supported"
}
```

#### Missing Required Field Error

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": "{\"First_Name\": \"John\"}"
  }
}
```

**Response:**

```json
{
  "success": false,
  "error": "REQUIRED_FIELD_MISSING",
  "error_type": "APIException",
  "message": "required field not found: Last_Name"
}
```

#### Picklist Field Type Error (Fixed)

The following error used to occur before the fix:

```
ERROR: TYPE ERROR - {"field": "Lead_Source", "class": "Record", "accepted_type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "given_type": "<class 'str'>"}
```

**Now Fixed:** The server automatically handles picklist fields by wrapping string values in `Choice` objects. You can pass picklist values as simple strings:

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": "{\"Last_Name\": \"Smith\", \"Lead_Source\": \"Web Research\"}"
  }
}
```

**Successful Response:**

```json
{
  "success": true,
  "id": "4876876000000240001",
  "message": "record added",
  "status": "success"
}
```

## Testing Examples

### Start MCP Server

```bash
python src/main.py
```

**Example Output:**

```
INFO:root:Zoho CRM SDK initialized successfully with FileStore
INFO:root:Starting Zoho CRM MCP Server...
INFO:root:Server running on http://0.0.0.0:8000/mcp/
INFO:root:Available tools: create_record, fetch_records, search_records
INFO:root:Available resources: module schema, fields, picklist values
```

### Test with Demo Mode

```bash
# Remove or comment out ZOHO_REFRESH_TOKEN in .env
python src/main.py
```

**Example Output:**

```
WARNING:root:Failed to initialize Zoho CRM SDK: No valid refresh token found. Please complete OAuth authorization first.
WARNING:root:Server will start in demo mode - API calls will return mock data
INFO:root:To fix this:
INFO:root:1. Run: python oauth_helper.py --get-auth-url
INFO:root:2. Visit the URL and grant permissions
INFO:root:3. Run: python oauth_helper.py --exchange-code YOUR_CODE
INFO:root:4. Update your .env file with the refresh token
INFO:root:Starting Zoho CRM MCP Server in demo mode...
```

## 🌍 Multi-Datacenter Examples

Multi-datacenter failover is **automatically enabled** for all API calls. No configuration needed!

### Create Record with Automatic Multi-DC

All `create_record` calls automatically use multi-datacenter failover:

```json
{
  "name": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": "{\"Last_Name\": \"Smith\", \"Company\": \"Global Corp\"}"
  }
}
```

**Successful Response (from any datacenter):**

```json
{
  "success": true,
  "data": [
    {
      "code": "SUCCESS",
      "details": {
        "Modified_Time": "2024-01-15T10:30:00+00:00",
        "Modified_By": {
          "name": "API User",
          "id": "*********"
        },
        "Created_Time": "2024-01-15T10:30:00+00:00",
        "id": "*********",
        "Created_By": {
          "name": "API User",
          "id": "*********"
        }
      },
      "message": "record added",
      "status": "success"
    }
  ],
  "_datacenter": "EU"
}
```

**Authentication Error Response:**

```json
{
  "success": false,
  "error": "AUTHENTICATION_ERROR",
  "datacenter": "US",
  "message": "Invalid access token"
}
```

### Primary Datacenter Configuration

Set your primary datacenter in your `.env` file:

```env
ZOHO_ENVIRONMENT=EU                    # Primary datacenter (US, EU, IN, AU, CN, JP)
ZOHO_ENVIRONMENT_TYPE=PRODUCTION       # Environment type
```

The system will automatically try your primary datacenter first, then failover to others if needed.

## 🔧 Multi-DC Features

The simplified multi-datacenter functionality provides:

- **🚀 Automatic Failover**: All API calls automatically try multiple datacenters
- **🎯 Primary First**: Your configured datacenter is tried first
- **⚡ First Success Wins**: Returns the first successful response
- **🔐 Smart Auth Detection**: Immediately returns authentication errors (token expired/invalid)
- **🛠️ Zero Configuration**: Works out-of-the-box, no setup required

This comprehensive examples guide covers all the tools, OAuth setup commands, multi-datacenter features, and common scenarios you'll encounter when using the Zoho CRM MCP Server.
