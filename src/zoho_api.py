#!/usr/bin/env python3
"""
Zoho CRM API client module
Handles all Zoho CRM REST API interactions with proper error handling and retry logic.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

import httpx
from .config import ZohoConfig
from .zoho_oauth import get_zoho_provider

logger = logging.getLogger(__name__)


class ZohoAPIError(Exception):
    """Custom exception for Zoho API errors"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(self.message)


class ZohoHTTPClient:
    """Internal HTTP client for Zoho API requests"""

    def __init__(self, config: ZohoConfig):
        self.config = config
        self.timeout = config.timeout
        self.max_retries = config.max_retries

        # HTTP client configuration
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "limits": httpx.Limits(max_keepalive_connections=10, max_connections=20),
            "follow_redirects": True,
        }

    async def make_request(
        self,
        method: str,
        url: str,
        access_token: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        refresh_token: Optional[str] = None,
        _retry_count: int = 0,
    ) -> Dict[str, Any]:
        """Make HTTP request to Zoho API with automatic token refresh"""

        headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
            "User-Agent": f"Zoho-CRM-MCP-Server/{self.config.api_version}-{self.config.environment}-{self.config.environment_type}",
        }

        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                logger.debug(
                    f"Making {method} request to {url} (retry: {_retry_count})"
                )

                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(
                        url, headers=headers, json=data, params=params
                    )
                elif method.upper() == "PUT":
                    response = await client.put(
                        url, headers=headers, json=data, params=params
                    )
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, params=params)
                else:
                    raise ZohoAPIError(f"Unsupported HTTP method: {method}")

                result = await self._handle_response(
                    response, access_token, refresh_token
                )

                # Handle token refresh and retry
                if (
                    result.get("error") == "TOKEN_REFRESHED"
                    and result.get("retry_with_new_token")
                    and _retry_count == 0  # Only retry once
                ):
                    new_access_token = result.get("new_access_token")
                    print("--------------------", new_access_token)
                    if new_access_token:
                        logger.info("Retrying request with refreshed token")
                        return await self.make_request(
                            method,
                            url,
                            new_access_token,
                            data,
                            params,
                            refresh_token,
                            _retry_count + 1,
                        )

                return result

        except httpx.TimeoutException:
            logger.warning(f"Request timeout for {method} {url}")
            raise ZohoAPIError(f"Request timeout")

        except httpx.RequestError as e:
            logger.error(f"Request error for {method} {url}: {e}")
            raise ZohoAPIError(f"Request failed: {str(e)}")

    async def _handle_response(
        self,
        response: httpx.Response,
        access_token: str,
        refresh_token: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Handle API response with proper error handling and token refresh"""

        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {"raw_response": response.text}

        # Success responses
        if response.status_code in [200, 201, 202]:
            return {
                "success": True,
                "data": response_data,
                "status_code": response.status_code,
            }

        # Handle specific error cases
        if response.status_code == 401:
            logger.warning(
                "Authentication failed - will be handled by multi-datacenter client"
            )
            return {
                "success": False,
                "error": "AUTHENTICATION_FAILED",
                "message": "Authentication failed",
                "status_code": 401,
                "details": response_data,
            }

        elif response.status_code == 400:
            # Check for rate limiting in 400 responses
            error_message = ""
            if isinstance(response_data, dict):
                error_message = response_data.get("message", "")
            elif isinstance(response_data, str):
                error_message = response_data

            if "too many requests" in error_message.lower():
                logger.warning("Rate limit detected in 400 response")
                return {
                    "success": False,
                    "error": "RATE_LIMITED",
                    "message": "Rate limited by Zoho API",
                    "status_code": 400,
                    "details": response_data,
                }
            else:
                logger.error(f"Bad request: {response_data}")
                return {
                    "success": False,
                    "error": "BAD_REQUEST",
                    "message": "Bad request",
                    "status_code": 400,
                    "details": response_data,
                }

        elif response.status_code == 403:
            logger.error("Access forbidden - insufficient permissions")
            return {
                "success": False,
                "error": "ACCESS_FORBIDDEN",
                "message": "Insufficient permissions for this operation",
                "status_code": 403,
                "details": response_data,
                "stop_processing": True,
            }

        elif response.status_code == 404:
            logger.warning(f"Resource not found")
            return {
                "success": False,
                "error": "RESOURCE_NOT_FOUND",
                "message": "Requested resource not found",
                "status_code": 404,
                "details": response_data,
            }

        elif response.status_code == 429:
            logger.warning("Rate limit exceeded")
            return {
                "success": False,
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "API rate limit exceeded",
                "status_code": 429,
                "details": response_data,
                "stop_processing": True,  # Stop trying other datacenters when rate limited
            }

        elif response.status_code >= 500:
            logger.error(f"Server error {response.status_code}")
            return {
                "success": False,
                "error": "SERVER_ERROR",
                "message": f"Zoho server error: {response.status_code}",
                "status_code": response.status_code,
                "details": response_data,
            }

        # Other client errors
        else:
            logger.error(f"API error {response.status_code}: {response_data}")
            return {
                "success": False,
                "error": f"HTTP_{response.status_code}",
                "message": f"API request failed: {response.reason_phrase}",
                "status_code": response.status_code,
                "details": response_data,
            }


class MultiDatacenterZohoClient:
    """Simplified multi-datacenter Zoho API client with automatic failover"""

    def __init__(self, base_config: ZohoConfig):
        self.base_config = base_config
        self.datacenters = ["US", "EU", "IN", "AU", "CN", "JP"]
        self.clients: Dict[str, ZohoHTTPClient] = {}

        # Token refresh coordination
        self._token_refresh_in_progress = False
        self._refreshed_token = None
        self._refresh_failed = False
        self._rate_limited = False
        self._successful_datacenter = (
            None  # Track which DC successfully refreshed token
        )

        # Initialize clients for all datacenters
        self._initialize_clients()

        logger.info(
            f"Multi-DC Zoho client initialized with {len(self.datacenters)} datacenters"
        )

    def _initialize_clients(self):
        """Initialize HTTP clients for all datacenters"""
        for dc in self.datacenters:
            try:
                # Create a config copy for each datacenter
                dc_config = ZohoConfig(
                    environment=dc,
                    environment_type=self.base_config.environment_type,
                    api_version=self.base_config.api_version,
                    timeout=self.base_config.timeout,
                    max_retries=1,  # Lower retries for faster failover
                    client_id=self.base_config.client_id,
                    client_secret=self.base_config.client_secret,
                )
                self.clients[dc] = ZohoHTTPClient(dc_config)
            except Exception as e:
                logger.error(f"Failed to initialize client for {dc}: {e}")

    def _reset_token_refresh_state(self):
        """Reset token refresh coordination state for new request"""
        self._token_refresh_in_progress = False
        self._refreshed_token = None
        self._refresh_failed = False
        self._rate_limited = False
        self._successful_datacenter = None

    def _get_prioritized_datacenters(self) -> List[str]:
        """Get datacenters with successful datacenter first, then primary, then others"""
        # If we have a successful datacenter from token refresh, prioritize it
        if self._successful_datacenter:
            successful_dc = self._successful_datacenter
            other_dcs = [dc for dc in self.datacenters if dc != successful_dc]
            logger.info(
                f"Prioritizing {successful_dc} datacenter (successful token refresh)"
            )
            return [successful_dc] + other_dcs

        # Otherwise, start with primary datacenter, then others
        primary_dc = self.base_config.environment
        other_dcs = [dc for dc in self.datacenters if dc != primary_dc]
        return [primary_dc] + other_dcs

    async def _call_single_datacenter(
        self, datacenter: str, method: str, endpoint: str, access_token: str, **kwargs
    ) -> Dict[str, Any]:
        """Make a call to a single datacenter"""
        client = self.clients.get(datacenter)

        if not client:
            return {
                "success": False,
                "error": "CLIENT_NOT_AVAILABLE",
                "datacenter": datacenter,
                "message": f"Client not available for {datacenter}",
            }

        # If we're rate limited, don't try any more requests
        if self._rate_limited:
            return {
                "success": False,
                "error": "RATE_LIMITED",
                "datacenter": datacenter,
                "message": "Rate limited - skipping datacenter",
            }

        try:
            # Extract refresh_token and json data from kwargs
            refresh_token = kwargs.pop("refresh_token", None)
            request_data = kwargs.pop("json", None)
            params = kwargs.pop("params", None)

            # Build the full URL for this datacenter
            dc_config = ZohoConfig(
                environment=datacenter,
                environment_type=self.base_config.environment_type,
                api_version=self.base_config.api_version,
                client_id=self.base_config.client_id,
                client_secret=self.base_config.client_secret,
            )
            url = f"{dc_config.api_base_url}/{endpoint.lstrip('/')}"

            # Use the refreshed token if available, otherwise use the original
            current_token = self._refreshed_token or access_token

            # Make the API call with refresh_token support
            response = await client.make_request(
                method,
                url,
                current_token,
                data=request_data,
                params=params,
                refresh_token=refresh_token,
            )

            # Handle authentication failures with centralized token refresh
            if response.get("status_code") == 401 and not self._refresh_failed:
                return await self._handle_auth_failure(
                    datacenter, response, access_token, refresh_token
                )

            # Check for rate limiting
            if response.get("status_code") == 400:
                error_details = response.get("details", {})
                error_message = ""
                if isinstance(error_details, dict):
                    error_message = error_details.get("message", "")
                elif isinstance(error_details, str):
                    error_message = error_details

                if "too many requests" in error_message.lower():
                    logger.warning(f"Rate limited detected from {datacenter}")
                    self._rate_limited = True
                    return {
                        "success": False,
                        "error": "RATE_LIMITED",
                        "datacenter": datacenter,
                        "message": "Rate limited by Zoho API",
                        "stop_processing": True,
                    }

            # Add datacenter info to response
            if isinstance(response, dict):
                response["_datacenter"] = datacenter

            return response

        except Exception as e:
            error_msg = str(e)

            # Check if it's an authentication error
            if any(
                auth_term in error_msg.lower()
                for auth_term in [
                    "invalid",
                    "token",
                    "expired",
                    "unauthorized",
                    "authentication",
                ]
            ):
                return {
                    "success": False,
                    "error": "AUTHENTICATION_ERROR",
                    "datacenter": datacenter,
                    "message": error_msg,
                }

            return {
                "success": False,
                "error": "DATACENTER_ERROR",
                "datacenter": datacenter,
                "message": error_msg,
            }

    async def _handle_auth_failure(
        self,
        datacenter: str,
        response: Dict[str, Any],
        access_token: str,
        refresh_token: str = None,
    ) -> Dict[str, Any]:
        """Handle authentication failure with centralized token refresh"""

        # If refresh already failed or no refresh token, return the auth error
        if self._refresh_failed or not refresh_token:
            return {
                "success": False,
                "error": "TOKEN_REFRESH_FAILED",
                "datacenter": datacenter,
                "message": "Authentication failed and no valid refresh token available",
                # Don't stop processing - other datacenters might work with original token
            }

        # If token refresh is already in progress, wait and use the result
        if self._token_refresh_in_progress:
            logger.info(
                f"Token refresh already in progress, waiting for result in {datacenter}"
            )
            return {
                "success": False,
                "error": "TOKEN_REFRESH_IN_PROGRESS",
                "datacenter": datacenter,
                "message": "Token refresh in progress",
            }

        # Start token refresh process
        self._token_refresh_in_progress = True
        logger.info(f"Starting centralized token refresh from {datacenter}")

        try:
            from .zoho_oauth import get_zoho_provider
            from .config import ZohoConfig

            # Create a config for the current datacenter to refresh token from that DC
            dc_config = ZohoConfig(
                environment=datacenter,
                environment_type=self.base_config.environment_type,
                api_version=self.base_config.api_version,
                client_id=self.base_config.client_id,
                client_secret=self.base_config.client_secret,
            )

            # Get the OAuth provider for the current datacenter and refresh the token
            oauth_provider = get_zoho_provider(dc_config)
            new_access_token = await oauth_provider.refresh_access_token(refresh_token)

            if new_access_token:
                logger.info(
                    f"Token refreshed successfully from {datacenter} - will be used for remaining datacenters"
                )
                self._refreshed_token = new_access_token
                self._successful_datacenter = datacenter  # Track which DC worked
                return {
                    "success": False,
                    "error": "TOKEN_REFRESHED",
                    "datacenter": datacenter,
                    "message": f"Token refreshed successfully from {datacenter}",
                    "new_access_token": new_access_token,
                    "retry_with_new_token": True,
                }
            else:
                logger.error("Token refresh failed")
                self._refresh_failed = True
                return {
                    "success": False,
                    "error": "TOKEN_REFRESH_FAILED",
                    "datacenter": datacenter,
                    "message": "Failed to refresh access token",
                    # Don't stop processing - other datacenters might work with original token
                }

        except Exception as e:
            logger.error(f"Error during token refresh: {e}")
            self._refresh_failed = True
            return {
                "success": False,
                "error": "TOKEN_REFRESH_ERROR",
                "datacenter": datacenter,
                "message": f"Error during token refresh: {str(e)}",
                # Don't stop processing - other datacenters might work with original token
            }

    async def multi_call(
        self, method: str, endpoint: str, access_token: str, **kwargs
    ) -> Dict[str, Any]:
        """
        Try datacenters in priority order and return first successful response.
        Stops immediately on success for optimal performance.
        """
        # Reset token refresh state for new request
        self._reset_token_refresh_state()

        prioritized_dcs = self._get_prioritized_datacenters()

        logger.info(f"Trying {len(prioritized_dcs)} datacenters in priority order")

        auth_error_result = None
        all_errors = []

        # Try each datacenter in priority order, stopping on first success
        for dc in prioritized_dcs:
            logger.debug(f"Trying datacenter: {dc}")

            try:
                result = await self._call_single_datacenter(
                    dc, method, endpoint, access_token, **kwargs
                )

                # Return immediately on success - STOP THE LOOP
                if result.get("success", False):
                    logger.info(
                        f"SUCCESS from {result.get('_datacenter')} - stopping other datacenter attempts"
                    )
                    return result

                # Stop processing if we hit rate limits or critical errors
                if result.get("stop_processing"):
                    logger.warning(
                        f"Stopping datacenter attempts due to: {result.get('error')}"
                    )
                    all_errors.append(result)
                    break

                # Collect errors for final response
                all_errors.append(result)

                # Store first authentication error
                if (
                    result.get("error") == "AUTHENTICATION_ERROR"
                    and not auth_error_result
                ):
                    auth_error_result = result
                    logger.warning(f"Authentication error from {dc}")
                else:
                    logger.warning(
                        f"Failed from {dc}: {result.get('error', 'Unknown error')}"
                    )

            except Exception as e:
                error_result = {
                    "success": False,
                    "error": "DATACENTER_EXCEPTION",
                    "datacenter": dc,
                    "message": str(e),
                }
                all_errors.append(error_result)
                logger.error(f"Exception from {dc}: {e}")

        # If we get here, all datacenters failed
        logger.error("All datacenters failed")

        # Return authentication error if we have one (most specific)
        if auth_error_result:
            logger.error("Returning authentication error as primary failure")
            return auth_error_result

        # Otherwise return general failure
        return {
            "success": False,
            "error": "ALL_DATACENTERS_FAILED",
            "message": f"All {len(prioritized_dcs)} datacenters failed",
            "datacenter_errors": all_errors,
        }

    # Convenience methods for common operations
    async def create_record(
        self,
        module: str,
        record_data: Dict[str, Any],
        access_token: str,
        refresh_token: str = None,
    ) -> Dict[str, Any]:
        """Create a record using multi-datacenter approach"""
        endpoint = f"/{module}"
        return await self.multi_call(
            "POST",
            endpoint,
            access_token,
            json={"data": [record_data]},
            refresh_token=refresh_token,
        )

    async def get_records(
        self, module: str, access_token: str, **params
    ) -> Dict[str, Any]:
        """Get records using multi-datacenter approach"""
        endpoint = f"/{module}"
        return await self.multi_call("GET", endpoint, access_token, params=params)

    async def search_records(
        self,
        module: str,
        criteria: str,
        access_token: str,
        refresh_token: str = None,
        page: int = 1,
        per_page: int = 200,
    ) -> Dict[str, Any]:
        """Search records using multi-datacenter approach"""
        endpoint = f"/{module}/search"
        params = {
            "criteria": criteria,
            "page": page,
            "per_page": min(per_page, 200),
        }
        return await self.multi_call(
            "GET", endpoint, access_token, params=params, refresh_token=refresh_token
        )

    async def search_by_phone(
        self,
        module: str,
        phone: str,
        access_token: str,
        refresh_token: str = None,
        page: int = 1,
        per_page: int = 200,
    ) -> Dict[str, Any]:
        """Search records by phone using Zoho's dedicated phone search endpoint"""
        endpoint = f"/{module}/search?phone={phone}"
        # params = {
        #     "phone": phone,
        #     "page": page,
        #     "per_page": min(per_page, 200),
        # }
        return await self.multi_call(
            "GET", endpoint, access_token, refresh_token=refresh_token
        )
