#!/usr/bin/env python3
"""
Authentication module for Zoho CRM MCP Server
Handles Bearer token extraction and validation.
"""

import logging
from typing import Optional, Dict, Any
from contextvars import ContextVar

from .config import SecurityConfig

logger = logging.getLogger(__name__)

# Context variables to store tokens for the current request
access_token_context: ContextVar[str] = ContextVar("access_token")
refresh_token_context: ContextVar[str] = ContextVar("refresh_token")


class AuthenticationError(Exception):
    """Custom exception for authentication errors"""

    def __init__(self, message: str, code: str = "AUTHENTICATION_FAILED"):
        self.message = message
        self.code = code
        super().__init__(self.message)


class AuthHandler:
    """Authentication handler for Bearer token validation"""

    def __init__(self, security_config: SecurityConfig):
        self.config = security_config
        self.auth_header = security_config.auth_header_name
        self.auth_scheme = security_config.auth_scheme
        self.require_auth = security_config.require_auth

    def extract_tokens_from_headers(
        self, headers: Dict[str, str]
    ) -> tuple[Optional[str], Optional[str]]:
        """Extract Bearer tokens from request headers (access token and refresh token)"""
        try:
            access_token = None
            refresh_token = None

            # Get authorization header (case-insensitive) for access token
            for key, value in headers.items():
                if key.lower() == self.auth_header.lower():
                    auth_header = value
                    # Parse Bearer token
                    parts = auth_header.split()
                    if len(parts) == 2:
                        scheme, token = parts
                        if scheme.lower() == self.auth_scheme.lower() and token.strip():
                            access_token = token.strip()
                    break

            # Get refresh token from X-Refresh-Token header
            for key, value in headers.items():
                if key.lower() == "x-refresh-token":
                    refresh_header = value
                    # Parse Bearer token format or direct token
                    if refresh_header.lower().startswith("bearer "):
                        refresh_token = refresh_header[7:].strip()
                    else:
                        refresh_token = refresh_header.strip()
                    break

            logger.debug(
                f"Extracted tokens - access: {'present' if access_token else 'missing'}, refresh: {'present' if refresh_token else 'missing'}"
            )
            return access_token, refresh_token

        except Exception as e:
            logger.error(f"Error extracting tokens: {e}")
            raise AuthenticationError(
                f"Error processing authentication: {str(e)}", "AUTH_PROCESSING_ERROR"
            )

    def extract_token_from_headers(self, headers: Dict[str, str]) -> Optional[str]:
        """Extract Bearer token from request headers (backward compatibility)"""
        access_token, _ = self.extract_tokens_from_headers(headers)
        return access_token

    def validate_token_format(self, token: str) -> bool:
        """Basic token format validation"""
        if not token:
            return False

        # Basic checks for token format
        if len(token) < 10:  # Minimum reasonable token length
            return False

        # Check for obvious invalid tokens
        invalid_tokens = ["test", "demo", "example", "placeholder"]
        if token.lower() in invalid_tokens:
            return False

        return True

    def set_access_token(self, token: str) -> None:
        """Set the access token in the current context"""
        if not self.validate_token_format(token):
            logger.warning("Invalid token format detected")
            raise AuthenticationError("Invalid token format", "INVALID_TOKEN_FORMAT")

        access_token_context.set(token)
        logger.debug("Access token set in context")

    def set_refresh_token(self, token: str) -> None:
        """Set the refresh token in the current context"""
        refresh_token_context.set(token)
        logger.debug("Refresh token set in context")

    def get_access_token(self) -> str:
        """Get the access token from the current context"""
        try:
            token = access_token_context.get()
            if not token:
                raise AuthenticationError(
                    "No access token found in request context", "NO_TOKEN_IN_CONTEXT"
                )
            return token
        except LookupError:
            raise AuthenticationError(
                "No access token found in request context", "NO_TOKEN_IN_CONTEXT"
            )

    def get_refresh_token(self) -> Optional[str]:
        """Get the refresh token from the current context"""
        try:
            return refresh_token_context.get()
        except LookupError:
            return None

    def create_auth_error_response(
        self, missing_access_token: bool = True, missing_refresh_token: bool = True
    ) -> Dict[str, Any]:
        """Create standardized authentication error response matching Google Calendar MCP format"""
        auth_requirements = []

        if missing_access_token:
            auth_requirements.append(
                {
                    "provider": "zoho",
                    "auth_type": "bearer",
                    "header_name": "Authorization",
                    "header_format": "Bearer {access_token}",
                    "required_scopes": [
                        "ZohoCRM.modules.ALL",
                        "ZohoCRM.settings.READ",
                    ],
                    "token_source": "access_token",
                }
            )

        if missing_refresh_token:
            auth_requirements.append(
                {
                    "provider": "zoho",
                    "auth_type": "bearer",
                    "header_name": "X-Refresh-Token",
                    "header_format": "Bearer {refresh_token}",
                    "token_source": "refresh_token",
                    "required_scopes": [
                        "ZohoCRM.modules.ALL",
                        "ZohoCRM.settings.READ",
                    ],
                }
            )

        return {
            "error": {
                "code": "AUTHENTICATION_REQUIRED",
                "message": "Authentication required to access this resource",
                "details": {"authentication_requirements": auth_requirements},
            }
        }

    def is_auth_required(self) -> bool:
        """Check if authentication is required"""
        return self.require_auth


def get_access_token() -> str:
    """Convenience function to get access token from context"""
    try:
        return access_token_context.get()
    except LookupError:
        raise AuthenticationError(
            "No access token found in request context", "NO_TOKEN_IN_CONTEXT"
        )


def set_access_token(token: str) -> None:
    """Convenience function to set access token in context"""
    access_token_context.set(token)


def get_refresh_token() -> Optional[str]:
    """Convenience function to get refresh token from context"""
    try:
        return refresh_token_context.get()
    except LookupError:
        return None


def set_refresh_token(token: str) -> None:
    """Convenience function to set refresh token in context"""
    refresh_token_context.set(token)
