"""
Pydantic schemas for Zoho CRM MCP server tools
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any


# CRM record management schemas
class CreateRecord(BaseModel):
    """Schema for creating a new Zoho CRM record"""

    module: str = Field(
        ...,
        description="Target Zoho CRM module",
        enum=[
            "Leads",
            "Contacts",
            "Accounts",
            "Deals",
            "Tasks",
            "Events",
            "Calls",
        ],
    )
    record_data: str = Field(
        ...,
        description='JSON stringified object containing field/value pairs for the new record. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date). Example: \'{"Last_Name": "Smith", "Company": "Acme Corp"}\'',
    )


class SearchByPhone(BaseModel):
    """Schema for searching CRM records by phone number"""

    phone_number: str = Field(
        ...,
        description="Phone number to search for across all CRM modules. Can be in any format (e.g., +**********, (*************, ************)",
        min_length=3,
    )


# class GetRecords(BaseModel):
#     """Schema for retrieving Zoho CRM records"""

#     module: str = Field(
#         ...,
#         description="Target Zoho CRM module",
#         enum=[
#             "Leads",
#             "Contacts",
#             "Accounts",
#             "Deals",
#             "Tasks",
#             "Events",
#             "Calls",
#         ],
#     )
#     fields: Optional[List[str]] = Field(
#         default=None,
#         description="Array of field names to retrieve (if not provided, returns all fields)",
#     )
#     page: Optional[int] = Field(default=1, description="Page number (minimum: 1)", ge=1)
#     per_page: Optional[int] = Field(
#         default=50,
#         description="Records per page (minimum: 1, maximum: 200)",
#         ge=1,
#         le=200,
#     )


# class SearchRecords(BaseModel):
#     """Schema for searching Zoho CRM records"""

#     module: str = Field(
#         ...,
#         description="Target Zoho CRM module",
#         enum=[
#             "Leads",
#             "Contacts",
#             "Accounts",
#             "Deals",
#             "Tasks",
#             "Events",
#             "Calls",
#         ],
#     )
#     search_criteria: str = Field(
#         ...,
#         description="Search criteria in Zoho format: (Field_Name:operator:value). Examples: (Last_Name:contains:Smith), ((Last_Name:contains:Smith)and(Lead_Source:equals:Website))",
#         min_length=3,
#     )
#     page: Optional[int] = Field(default=1, description="Page number (minimum: 1)", ge=1)
#     per_page: Optional[int] = Field(
#         default=200,
#         description="Records per page (minimum: 1, maximum: 200)",
#         ge=1,
#         le=200,
#     )


# class HealthCheck(BaseModel):
#     """Schema for health check - no parameters required"""

#     pass


# class GetToolInfo(BaseModel):
#     """Schema for getting tool information"""

#     tool_name: Optional[str] = Field(
#         default=None,
#         description="Specific tool name to get information for",
#         enum=[
#             "create_record",
#             "get_records",
#             "search_records",
#             "health_check",
#             "get_tool_info",
#         ],
#     )
