#!/usr/bin/env python3
"""
Configuration module for Zoho CRM MCP Server
Handles all environment variables and settings for production deployment.
"""

import os
from typing import Dict, Optional
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """Server configuration settings"""

    host: str = Field(
        default="0.0.0.0", alias="SERVER_HOST", description="Server host address"
    )
    port: int = Field(
        default=8000, alias="SERVER_PORT", description="Server port", ge=1, le=65535
    )
    name: str = Field(
        default="Zoho CRM MCP Server", alias="SERVER_NAME", description="Server name"
    )
    version: str = Field(
        default="1.0.0", alias="SERVER_VERSION", description="Server version"
    )
    debug: bool = Field(
        default=False, alias="SERVER_DEBUG", description="Enable debug mode"
    )
    log_level: str = Field(
        default="INFO", alias="SERVER_LOG_LEVEL", description="Logging level"
    )
    path: str = Field(
        default="/mcp", alias="SERVER_PATH", description="Server endpoint path"
    )

    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"log_level must be one of {valid_levels}")
        return v.upper()


class ZohoConfig(BaseSettings):
    """Zoho CRM API configuration with multi-DC support"""

    environment: str = Field(
        default="US",
        description="Zoho data center environment",
    )
    environment_type: str = Field(
        default="PRODUCTION",
        description="Environment type (PRODUCTION, DEVELOPER, SANDBOX)",
    )
    base_url: Optional[str] = Field(
        default=None, description="Custom base URL override"
    )
    api_version: str = Field(default="v8", description="Zoho API version")
    timeout: int = Field(default=30, description="API request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum API retry attempts")

    # OAuth configuration for token refresh
    client_id: Optional[str] = Field(
        default=None, description="Zoho OAuth client ID for token refresh"
    )
    client_secret: Optional[str] = Field(
        default=None, description="Zoho OAuth client secret for token refresh"
    )

    class Config:
        env_prefix = "ZOHO_"
        case_sensitive = False

    @validator("environment")
    def validate_environment(cls, v):
        valid_envs = ["US", "EU", "IN", "AU", "CN", "JP"]
        if v.upper() not in valid_envs:
            raise ValueError(f"environment must be one of {valid_envs}")
        return v.upper()

    @validator("environment_type")
    def validate_environment_type(cls, v):
        valid_types = ["PRODUCTION", "DEVELOPER", "SANDBOX"]
        if v.upper() not in valid_types:
            raise ValueError(f"environment_type must be one of {valid_types}")
        return v.upper()

    @property
    def api_base_url(self) -> str:
        """Get the API base URL based on environment and type"""
        if self.base_url:
            return self.base_url

        # Multi-DC URL mapping based on Zoho v8 SDK patterns
        env_urls = {
            "US": "https://www.zohoapis.com",
            "EU": "https://www.zohoapis.eu",
            "IN": "https://www.zohoapis.in",
            "AU": "https://www.zohoapis.com.au",
            "CN": "https://www.zohoapis.com.cn",
            "JP": "https://www.zohoapis.jp",
        }

        base_domain = env_urls.get(self.environment, env_urls["US"])

        # Handle different environment types
        if self.environment_type == "SANDBOX":
            # Sandbox environments typically use sandbox subdomain
            base_domain = base_domain.replace("www.", "sandbox-")
        elif self.environment_type == "DEVELOPER":
            # Developer environments typically use developer subdomain
            base_domain = base_domain.replace("www.", "developer-")

        return f"{base_domain}/crm/{self.api_version}"

    @property
    def oauth_token_url(self) -> str:
        """Get the OAuth token URL based on environment"""
        # Multi-DC OAuth URL mapping
        env_urls = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
            "AU": "https://accounts.zoho.com.au",
            "CN": "https://accounts.zoho.com.cn",
            "JP": "https://accounts.zoho.jp",
        }

        base_domain = env_urls.get(self.environment, env_urls["US"])
        return f"{base_domain}/oauth/v2/token"

    @property
    def datacenter_info(self) -> Dict[str, str]:
        """Get datacenter information for logging and debugging"""
        return {
            "datacenter": self.environment,
            "environment_type": self.environment_type,
            "api_version": self.api_version,
            "base_url": self.api_base_url,
            "oauth_token_url": self.oauth_token_url,
        }


class CORSConfig(BaseModel):
    """CORS configuration for the server"""

    allow_origins: list = Field(
        default=["*"], alias="CORS_ALLOW_ORIGINS", description="Allowed origins"
    )
    allow_methods: list = Field(
        default=["*"], alias="CORS_ALLOW_METHODS", description="Allowed HTTP methods"
    )
    allow_headers: list = Field(
        default=["*"], alias="CORS_ALLOW_HEADERS", description="Allowed headers"
    )
    allow_credentials: bool = Field(
        default=True, alias="CORS_ALLOW_CREDENTIALS", description="Allow credentials"
    )
    max_age: int = Field(
        default=600, alias="CORS_MAX_AGE", description="Preflight cache max age"
    )


class SecurityConfig(BaseModel):
    """Security configuration"""

    require_auth: bool = Field(default=True, description="Require authentication")
    auth_header_name: str = Field(
        default="Authorization", description="Auth header name"
    )
    auth_scheme: str = Field(default="Bearer", description="Auth scheme")
    rate_limit_enabled: bool = Field(default=False, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, description="Requests per minute")


class Settings(BaseSettings):
    """Main application settings"""

    # Server configuration
    server: ServerConfig = Field(default_factory=ServerConfig)

    # Zoho configuration
    zoho: ZohoConfig = Field(default_factory=ZohoConfig)

    # CORS configuration
    cors: CORSConfig = Field(default_factory=CORSConfig)

    # Security configuration
    security: SecurityConfig = Field(default_factory=SecurityConfig)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
        extra = "ignore"

    @classmethod
    def from_env(cls) -> "Settings":
        """Create settings from environment variables"""
        # Explicitly load .env file
        try:
            from dotenv import load_dotenv

            load_dotenv()
        except ImportError:
            # python-dotenv not installed, continue without it
            pass

        # Debug: Check if environment variables are loaded
        import logging

        logger = logging.getLogger(__name__)
        client_id = os.getenv("ZOHO_CLIENT_ID")
        client_secret = os.getenv("ZOHO_CLIENT_SECRET")
        logger.info(f"Loading ZOHO_CLIENT_ID: {'present' if client_id else 'missing'}")
        logger.info(
            f"Loading ZOHO_CLIENT_SECRET: {'present' if client_secret else 'missing'}"
        )
        if client_id:
            logger.info(f"ZOHO_CLIENT_ID value: {client_id[:10]}...")
        if client_secret:
            logger.info(f"ZOHO_CLIENT_SECRET value: {client_secret[:10]}...")

        return cls(
            server=ServerConfig(
                host=os.getenv("SERVER_HOST", "127.0.0.1"),
                port=int(os.getenv("SERVER_PORT", "8000")),
                name=os.getenv("SERVER_NAME", "Zoho CRM MCP Server"),
                version=os.getenv("SERVER_VERSION", "1.0.0"),
                debug=os.getenv("SERVER_DEBUG", "false").lower() == "true",
                log_level=os.getenv("SERVER_LOG_LEVEL", "INFO"),
                path=os.getenv("SERVER_PATH", "/mcp"),
            ),
            zoho=ZohoConfig(
                environment=os.getenv("ZOHO_ENVIRONMENT", "US"),
                environment_type=os.getenv("ZOHO_ENVIRONMENT_TYPE", "PRODUCTION"),
                base_url=os.getenv("ZOHO_BASE_URL"),
                api_version=os.getenv("ZOHO_API_VERSION", "v8"),
                timeout=int(os.getenv("ZOHO_TIMEOUT", "30")),
                max_retries=int(os.getenv("ZOHO_MAX_RETRIES", "3")),
                client_id=os.getenv("ZOHO_CLIENT_ID"),
                client_secret=os.getenv("ZOHO_CLIENT_SECRET"),
            ),
            cors=CORSConfig(
                allow_origins=os.getenv("CORS_ALLOW_ORIGINS", "*").split(","),
                allow_methods=os.getenv("CORS_ALLOW_METHODS", "*").split(","),
                allow_headers=os.getenv("CORS_ALLOW_HEADERS", "*").split(","),
                allow_credentials=os.getenv("CORS_ALLOW_CREDENTIALS", "true").lower()
                == "true",
                max_age=int(os.getenv("CORS_MAX_AGE", "600")),
            ),
            security=SecurityConfig(
                require_auth=os.getenv("SECURITY_REQUIRE_AUTH", "true").lower()
                == "true",
                auth_header_name=os.getenv("SECURITY_AUTH_HEADER", "Authorization"),
                auth_scheme=os.getenv("SECURITY_AUTH_SCHEME", "Bearer"),
                rate_limit_enabled=os.getenv(
                    "SECURITY_RATE_LIMIT_ENABLED", "false"
                ).lower()
                == "true",
                rate_limit_requests=int(
                    os.getenv("SECURITY_RATE_LIMIT_REQUESTS", "100")
                ),
            ),
        )

    def get_server_url(self) -> str:
        """Get the full server URL"""
        return f"http://{self.server.host}:{self.server.port}"

    def get_mcp_url(self) -> str:
        """Get the full MCP endpoint URL"""
        return f"http://{self.server.host}:{self.server.port}{self.server.path}"


# Global settings instance
settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance"""
    global settings
    if settings is None:
        settings = Settings.from_env()
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment"""
    global settings
    settings = Settings.from_env()
    return settings
